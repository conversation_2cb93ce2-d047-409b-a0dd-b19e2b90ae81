{"title": "Human Resource Management (HRM)", "description": "Manage human resources and recruitment", "modules": {"employees": {"title": "Employees", "description": "Manage employee information", "countLabel": "Employees"}, "departments": {"title": "Departments", "description": "Manage organizational structure", "countLabel": "Departments"}, "recruitment": {"title": "Recruitment", "description": "Manage recruitment process", "countLabel": "Positions"}, "attendance": {"title": "Attendance", "description": "Manage attendance and working hours", "countLabel": "Days"}, "leave": {"title": "Leave", "description": "Manage leave and absences", "countLabel": "Requests"}, "payroll": {"title": "Payroll", "description": "Manage salary and benefits", "countLabel": "Payroll period"}, "training": {"title": "Training", "description": "Manage training and development", "countLabel": "Courses"}}, "employee": {"title": "Employee Management", "form": {"title": "Add New Employee", "description": "Enter new employee information"}, "table": {"employeeCode": "Emp ID", "jobTitle": "Job Title", "department": "Department", "status": "Status", "hireDate": "Hire Date"}, "actions": {"roles": "Roles", "permissions": "Permissions"}, "roleModal": {"title": "Role Assignment"}, "permissionModal": {"title": "Direct Permissions"}, "permission": {"title": "Employee Role Assignment", "description": "Select roles to assign to this employee"}, "directPermission": {"title": "Direct Permission Assignment", "description": "Select permissions to directly assign to this employee"}}, "permission": {"permissionCount": "{{count}} permissions", "noRoles": "No roles available", "noPermissions": "No permissions available", "form": {"permissions": "Permissions"}}, "department": {"title": "Department Management", "form": {"title": "Add New Department", "description": "Description", "name": "Department Name", "parentDepartment": "Parent Department", "manager": "Manager"}, "table": {"name": "Department Name", "description": "Description", "parentDepartment": "Parent Department", "manager": "Manager"}, "filter": {"noParent": "Top Level Departments", "hasParent": "Sub Departments"}, "bulkDeleteConfirmMessage": "Are you sure you want to delete {{count}} selected departments?", "tree": {"title": "Organizational Structure", "noDepartments": "No departments", "totalDepartments": "Total: {{count}} departments"}}}