import { SortDirection } from '@/shared/dto/request/query.dto';

/**
 * Interface cho dữ liệu phòng ban
 */
export interface DepartmentDto {
  id: number;
  name: string;
  description: string | null;
  managerId: number | null;
  parentId: number | null;
  createdAt: number | null;
  tenantId: number | null;
}

/**
 * Interface cho tham số truy vấn danh sách phòng ban
 */
export interface DepartmentQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: SortDirection;
  managerId?: number;
  parentId?: number | null;
}

/**
 * Interface cho dữ liệu tạo phòng ban mới
 */
export interface CreateDepartmentDto {
  name: string;
  description?: string;
  managerId?: number;
  parentId?: number;
}

/**
 * Interface cho dữ liệu cập nhật phòng ban
 */
export interface UpdateDepartmentDto {
  name?: string;
  description?: string;
  managerId?: number;
  parentId?: number;
}

/**
 * Interface cho nút trong cây phòng ban
 */
export interface DepartmentTreeNodeDto {
  id: number;
  name: string;
  parentId: number | null;
  managerId: number | null;
  managerName?: string | null;
  description?: string | null;
  children?: DepartmentTreeNodeDto[];
}

/**
 * Interface cho cấu trúc cây phòng ban
 */
export interface DepartmentTreeResponseDto {
  departments: DepartmentTreeNodeDto[];
  totalDepartments: number;
}

/**
 * Interface cho thành viên phòng ban
 */
export interface DepartmentMemberDto {
  id: number;
  fullName: string;
  email: string;
  position: string | null;
  phoneNumber: string | null;
  avatarUrl: string | null;
  status: string;
  isManager: boolean;
}

/**
 * Interface cho danh sách thành viên phòng ban
 */
export interface DepartmentMembersResponseDto {
  departmentId: number;
  departmentName: string;
  members: DepartmentMemberDto[];
  totalMembers: number;
  managerId: number | null;
}

/**
 * Interface cho dữ liệu xóa nhiều phòng ban
 */
export interface BulkDeleteDepartmentDto {
  ids: number[];
}

/**
 * Interface cho kết quả xóa nhiều
 */
export interface BulkDeleteResponseDto {
  successCount: number;
  failedCount: number;
  failedIds: number[];
  errors: string[];
}
