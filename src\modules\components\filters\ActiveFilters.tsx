import { SortDirection } from '@/shared/dto/request/query.dto';
import { format } from 'date-fns';
import { vi } from 'date-fns/locale';
import React from 'react';
import { useTranslation } from 'react-i18next';

export interface FilterTag {
  id: string;
  label: string;
  value: string;
  onRemove: () => void;
}

interface ActiveFiltersProps {
  searchTerm?: string;
  onClearSearch?: () => void;
  filterValue?: string | number | boolean;
  filterLabel?: string;
  onClearFilter?: () => void;
  dateRange?: [Date | null, Date | null];
  onClearDateRange?: () => void;
  sortBy?: string | null;
  sortDirection?: SortDirection | null;
  onClearSort?: () => void;
  customTags?: FilterTag[];
  onClearAll?: () => void;
}

/**
 * Component hiển thị các điều kiện lọc đang được áp dụng
 */
const ActiveFilters: React.FC<ActiveFiltersProps> = ({
  searchTerm,
  onClearSearch,
  filterValue,
  filterLabel,
  onClearFilter,
  dateRange,
  onClearDateRange,
  sortBy,
  sortDirection,
  onClearSort,
  customTags = [],
  onClearAll,
}) => {
  const { t } = useTranslation(['common']);
  const hasActiveFilters =
    (searchTerm && searchTerm.trim() !== '') ||
    (filterValue && filterValue !== 'all') ||
    (dateRange && (dateRange[0] || dateRange[1])) ||
    (sortBy && sortDirection) ||
    customTags.length > 0;

  if (!hasActiveFilters) {
    return null;
  }

  const formatDate = (date: Date | null) => {
    if (!date) return '';
    return format(date, 'dd/MM/yyyy', { locale: vi });
  };

  const getSortDirectionLabel = (direction: SortDirection | null) => {
    if (!direction) return '';
    return direction === SortDirection.ASC ? t('common:ascending') : t('common:descending');
  };

  console.log(onClearAll);

  return (
    <div className="mb-4">
      <div className="flex flex-wrap items-center gap-2">
        {/* Tìm kiếm */}
        {searchTerm && searchTerm.trim() !== '' && (
          <div className="flex items-center bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 px-2 py-1 rounded-md border border-blue-100 dark:border-blue-800">
            <span className="text-sm mr-1">{t('common:search')}:</span>
            <span className="text-sm font-medium">{searchTerm}</span>
            {onClearSearch && (
              <button
                className="ml-1 p-1 rounded-full hover:bg-blue-100 dark:hover:bg-blue-800 flex items-center justify-center"
                onClick={onClearSearch}
                aria-label={t('common:clear')}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-blue-700 dark:text-blue-300"
                >
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            )}
          </div>
        )}

        {/* Bộ lọc */}
        {filterValue && filterValue !== 'all' && (
          <div className="flex items-center bg-purple-50 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300 px-2 py-1 rounded-md border border-purple-100 dark:border-purple-800">
            <span className="text-sm mr-1">{t('common:filter')}:</span>
            <span className="text-sm font-medium">{filterLabel || filterValue.toString()}</span>
            {onClearFilter && (
              <button
                className="ml-1 p-1 rounded-full hover:bg-purple-100 dark:hover:bg-purple-800 flex items-center justify-center"
                onClick={onClearFilter}
                aria-label={t('common:clear')}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-purple-700 dark:text-purple-300"
                >
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            )}
          </div>
        )}

        {/* Khoảng thời gian */}
        {dateRange && (dateRange[0] || dateRange[1]) && (
          <div className="flex items-center bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-300 px-2 py-1 rounded-md border border-green-100 dark:border-green-800">
            <span className="text-sm mr-1">{t('common:dateRange')}:</span>
            <span className="text-sm font-medium">
              {dateRange[0] ? formatDate(dateRange[0]) : t('common:fromBeginning')} -{' '}
              {dateRange[1] ? formatDate(dateRange[1]) : t('common:toEnd')}
            </span>
            {onClearDateRange && (
              <button
                className="ml-1 p-1 rounded-full hover:bg-green-100 dark:hover:bg-green-800 flex items-center justify-center"
                onClick={onClearDateRange}
                aria-label={t('common:clear')}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-green-700 dark:text-green-300"
                >
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            )}
          </div>
        )}

        {/* Sắp xếp */}
        {sortBy && sortDirection && (
          <div className="flex items-center bg-amber-50 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300 px-2 py-1 rounded-md border border-amber-100 dark:border-amber-800">
            <span className="text-sm mr-1">{t('common:sortBy')}:</span>
            <span className="text-sm font-medium">
              {sortBy} ({getSortDirectionLabel(sortDirection)})
            </span>
            {onClearSort && (
              <button
                className="ml-1 p-1 rounded-full hover:bg-amber-100 dark:hover:bg-amber-800 flex items-center justify-center"
                onClick={onClearSort}
                aria-label={t('common:clear')}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-amber-700 dark:text-amber-300"
                >
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            )}
          </div>
        )}

        {/* Custom tags */}
        {customTags.map(tag => (
          <div
            key={tag.id}
            className="flex items-center bg-gray-50 text-gray-700 dark:bg-gray-800 dark:text-gray-300 px-2 py-1 rounded-md border border-gray-100 dark:border-gray-700"
          >
            <span className="text-sm mr-1">{tag.label}:</span>
            <span className="text-sm font-medium">{tag.value}</span>
            <button
              className="ml-1 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 flex items-center justify-center"
              onClick={tag.onRemove}
              aria-label={t('common:clear')}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2.5"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-gray-700 dark:text-gray-300"
              >
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ActiveFilters;
