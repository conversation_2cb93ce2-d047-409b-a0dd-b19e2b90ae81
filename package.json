{"name": "template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:staging": "vite --mode staging", "dev:production": "vite --mode production", "dev:testing": "vite --mode testing", "dev:localhost": "vite --mode localhost", "dev:host": "vite --host", "prebuild": "npm run strict-check", "build": "vite build", "prebuild:staging": "npm run strict-check", "build:staging": "vite build --mode staging", "prebuild:production": "npm run strict-check", "build:production": "vite build --mode production", "prebuild:testing": "npm run strict-check", "build:testing": "vite build --mode testing", "prebuild:localhost": "npm run strict-check", "build:localhost": "vite build --mode localhost", "strict-check": "node scripts/pre-build-check.js", "build:widget": "vite build --config vite.widget.config.ts", "build:widget:production": "vite build --config vite.widget.config.ts --mode production", "preview:widget": "vite preview --config vite.widget.config.ts", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint src --ext ts,tsx --fix", "lint:style": "stylelint \"src/**/*.{css,scss}\"", "lint:style:fix": "stylelint \"src/**/*.{css,scss}\" --fix", "lint:all": "npm run lint && npm run lint:style && npm run format:check", "lint:all:fix": "npm run lint:fix && npm run lint:style:fix && npm run format", "preview": "vite preview", "preview:staging": "vite preview --mode staging", "preview:production": "vite preview --mode production", "preview:testing": "vite preview --mode testing", "preview:localhost": "vite preview --mode localhost", "preview:host": "vite preview --host", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,css,scss,md,json}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,css,scss,md,json}\"", "prebuild-disabled": "npm run type-check", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "prepare": "husky", "check-all": "npm run lint:all && npm run type-check && npm run test", "fix-all": "npm run lint:all:fix"}, "dependencies": {"@dagrejs/dagre": "^1.1.4", "@floating-ui/react": "^0.27.8", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/list": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@hookform/resolvers": "^5.0.1", "@reduxjs/toolkit": "^2.7.0", "@tanstack/react-query": "^5.75.7", "@tanstack/react-query-devtools": "^5.75.7", "@types/dompurify": "^3.0.5", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-transition-group": "^4.4.12", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "browser-image-compression": "^2.0.2", "clsx": "^2.1.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dompurify": "^3.2.5", "framer-motion": "^12.9.4", "gantt-task-react": "^0.3.9", "i18next": "^25.0.2", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "react": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-grid-layout": "^1.5.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.56.2", "react-i18next": "^15.5.1", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.5.3", "react-syntax-highlighter": "^15.6.1", "react-transition-group": "^4.4.5", "reactflow": "^11.11.4", "recharts": "^2.15.3", "redux-persist": "^6.0.0", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.2.0", "uuid": "^11.1.0", "zod": "^3.24.4"}, "devDependencies": {"@eslint/js": "^9.22.0", "@testing-library/jest-dom": "^6.3.0", "@testing-library/react": "^14.1.2", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.16", "@types/node": "^22.15.3", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "chalk": "^5.4.1", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-perfectionist": "^4.13.0", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-security": "^3.0.1", "eslint-plugin-sonarjs": "^3.0.2", "eslint-plugin-unicorn": "^59.0.1", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^16.0.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "rollup-plugin-visualizer": "^5.14.0", "run-parallel": "^1.2.0", "stylelint": "^15.11.0", "stylelint-config-standard": "^34.0.0", "tailwindcss": "^3.3.5", "terser": "^5.39.2", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1", "vite-plugin-eslint": "^1.8.1", "vitest": "^1.3.1"}}