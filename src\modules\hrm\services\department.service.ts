import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

import {
  CreateDepartmentDto,
  DepartmentDto,
  DepartmentMembersResponseDto,
  DepartmentQueryDto,
  DepartmentTreeResponseDto,
  UpdateDepartmentDto,
} from '../types/department.types';

/**
 * Service cho các API liên quan đến phòng ban
 */
export const DepartmentService = {
  /**
   * Lấy danh sách phòng ban với phân trang và lọc
   * @param params Tham số truy vấn
   * @returns Danh sách phòng ban đã phân trang
   */
  async getDepartments(
    params?: DepartmentQueryDto
  ): Promise<ApiResponseDto<PaginatedResult<DepartmentDto>>> {
    return apiClient.get<PaginatedResult<DepartmentDto>>('/api/hrm/departments', {
      params,
    });
  },

  /**
   * Lấy chi tiết phòng ban theo ID
   * @param id ID phòng ban
   * @returns Chi tiết phòng ban
   */
  async getDepartment(id: number): Promise<ApiResponseDto<DepartmentDto>> {
    return apiClient.get<DepartmentDto>(`/api/hrm/departments/${id}`);
  },

  /**
   * Tạo mới phòng ban
   * @param data Dữ liệu tạo phòng ban
   * @returns Phòng ban đã tạo
   */
  async createDepartment(data: CreateDepartmentDto): Promise<ApiResponseDto<DepartmentDto>> {
    return apiClient.post<DepartmentDto>('/api/hrm/departments', data);
  },

  /**
   * Cập nhật phòng ban
   * @param id ID phòng ban
   * @param data Dữ liệu cập nhật
   * @returns Phòng ban đã cập nhật
   */
  async updateDepartment(
    id: number,
    data: UpdateDepartmentDto
  ): Promise<ApiResponseDto<DepartmentDto>> {
    return apiClient.put<DepartmentDto>(`/api/hrm/departments/${id}`, data);
  },

  /**
   * Xóa phòng ban
   * @param id ID phòng ban
   * @returns Kết quả xóa
   */
  async deleteDepartment(id: number): Promise<ApiResponseDto<boolean>> {
    return apiClient.delete<boolean>(`/api/hrm/departments/${id}`);
  },

  /**
   * Lấy cấu trúc cây phòng ban
   * @returns Cấu trúc cây phòng ban
   */
  async getDepartmentTree(): Promise<ApiResponseDto<DepartmentTreeResponseDto>> {
    return apiClient.get<DepartmentTreeResponseDto>('/api/hrm/departments/tree');
  },

  /**
   * Lấy danh sách thành viên trong phòng ban
   * @param id ID phòng ban
   * @returns Danh sách thành viên
   */
  async getDepartmentMembers(id: number): Promise<ApiResponseDto<DepartmentMembersResponseDto>> {
    return apiClient.get<DepartmentMembersResponseDto>(`/api/hrm/departments/${id}/members`);
  },

  /**
   * Xóa nhiều phòng ban
   * @param data Dữ liệu xóa nhiều phòng ban
   * @returns Kết quả xóa nhiều
   */
  async bulkDeleteDepartments(data: { ids: number[] }): Promise<
    ApiResponseDto<{
      successCount: number;
      failedCount: number;
      failedIds: number[];
      errors: string[];
    }>
  > {
    return apiClient.delete<{
      successCount: number;
      failedCount: number;
      failedIds: number[];
      errors: string[];
    }>('/api/hrm/departments/bulk-delete', { data });
  },
};
