import React from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import {
  Button,
  CollapsibleCard,
  DatePicker,
  Form,
  FormItem,
  Icon,
  Input,
  Select,
  Textarea,
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks';

import { loadDepartmentsForAsyncSelect } from '../../hooks/useDepartments';
import { loadEmployeesForAsyncSelect } from '../../hooks/useEmployees';
import { createEmployeeSchema } from '../../schemas/employee.schema';
import { EmployeeStatus, EmploymentType, MaritalStatus } from '../../types/employee.types';

// Định nghĩa props cho component
interface EmployeeFormProps {
  initialData?: z.infer<typeof createEmployeeSchema>;
  onSubmit: (data: z.infer<typeof createEmployeeSchema>) => void;
  onCancel: () => void;
  isSubmitting?: boolean;
}

/**
 * Form tạo/cập nhật nhân viên
 */
// Import trực tiếp các hàm cần thiết

const EmployeeForm: React.FC<EmployeeFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
}) => {
  const { t } = useTranslation(['hrm', 'common']);
  const isEditMode = !!initialData?.userId;

  // Import các hàm cần thiết
  const AsyncSelectWithPagination = React.lazy(
    () => import('@/shared/components/common/Select/AsyncSelectWithPagination')
  );

  // Sử dụng hook useFormErrors để quản lý lỗi form
  const { formRef, setFormErrors } = useFormErrors<z.infer<typeof createEmployeeSchema>>();

  // Xử lý submit form
  const handleSubmit = (values: unknown) => {
    try {
      // Reset form errors
      setFormErrors({});

      // Type assertion to the correct type
      const employeeValues = values as z.infer<typeof createEmployeeSchema>;

      // Call the onSubmit callback
      onSubmit(employeeValues);
    } catch (error) {
      console.error('Error submitting employee form:', error);

      // Check if error has field-specific errors
      if (error && typeof error === 'object' && 'response' in error && error.response) {
        const axiosError = error as {
          response: { data?: { message?: string; errors?: Record<string, string> } };
        };

        // If there are field-specific errors, set them
        if (axiosError.response.data?.errors) {
          setFormErrors(axiosError.response.data.errors);
        }
      }
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold mb-6">
        {isEditMode
          ? t('hrm:employee.form.editTitle', 'Cập nhật thông tin nhân viên')
          : t('hrm:employee.form.createTitle', 'Thêm nhân viên mới')}
      </h2>

      <Form
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        ref={formRef as any}
        schema={createEmployeeSchema}
        onSubmit={handleSubmit}
        className="space-y-6"
        defaultValues={
          initialData || {
            status: EmployeeStatus.ACTIVE,
          }
        }
      >
        <CollapsibleCard
          title={
            <h3 className="text-lg font-medium p-3">
              {t('hrm:employee.form.basicInfo', 'Thông tin cơ bản')}
            </h3>
          }
          defaultOpen={true}
        >
          <div className="space-y-4">
            <FormItem
              name="employeeCode"
              label={t('hrm:employee.form.employeeCode', 'Mã nhân viên')}
              required
            >
              <Input leftIcon={<Icon name="code" size="sm" />} fullWidth />
            </FormItem>

            <FormItem name="jobTitle" label={t('hrm:employee.form.jobTitle', 'Chức danh')}>
              <Input leftIcon={<Icon name="file-text" size="sm" />} fullWidth />
            </FormItem>

            <FormItem name="jobLevel" label={t('hrm:employee.form.jobLevel', 'Cấp bậc')}>
              <Input leftIcon={<Icon name="trending-up" size="sm" />} fullWidth />
            </FormItem>

            <FormItem name="departmentId" label={t('hrm:employee.form.departmentId', 'Phòng ban')}>
              <AsyncSelectWithPagination
                loadOptions={loadDepartmentsForAsyncSelect}
                placeholder={t('hrm:employee.form.selectDepartment', 'Chọn phòng ban')}
                fullWidth
              />
            </FormItem>

            <FormItem name="managerId" label={t('hrm:employee.form.managerId', 'Quản lý')}>
              <AsyncSelectWithPagination
                loadOptions={loadEmployeesForAsyncSelect}
                placeholder={t('hrm:employee.form.selectManager', 'Chọn quản lý')}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="employmentType"
              label={t('hrm:employee.form.employmentType', 'Loại hợp đồng')}
            >
              <Select
                placeholder={t('hrm:employee.form.selectEmploymentType', 'Chọn loại hợp đồng')}
                options={Object.values(EmploymentType).map(type => ({
                  value: type,
                  label: t(`hrm:employee.employmentType.${type}`, type),
                }))}
                fullWidth
              />
            </FormItem>

            <FormItem name="status" label={t('hrm:employee.form.status', 'Trạng thái')}>
              <Select
                placeholder={t('hrm:employee.form.selectStatus', 'Chọn trạng thái')}
                options={Object.values(EmployeeStatus).map(status => ({
                  value: status,
                  label: t(`hrm:employee.status.${status}`, status),
                }))}
                fullWidth
              />
            </FormItem>

            <FormItem name="hireDate" label={t('hrm:employee.form.hireDate', 'Ngày vào làm')}>
              <DatePicker placeholder={t('hrm:employee.form.selectDate', 'Chọn ngày')} fullWidth />
            </FormItem>
          </div>
        </CollapsibleCard>

        <CollapsibleCard
          title={
            <h3 className="text-lg font-medium p-3">
              {t('hrm:employee.form.personalInfo', 'Thông tin cá nhân')}
            </h3>
          }
          defaultOpen={true}
        >
          <div className="space-y-4">
            <FormItem name="dateOfBirth" label={t('hrm:employee.form.dateOfBirth', 'Ngày sinh')}>
              <DatePicker placeholder={t('hrm:employee.form.selectDate', 'Chọn ngày')} fullWidth />
            </FormItem>

            <FormItem name="gender" label={t('hrm:employee.form.gender', 'Giới tính')}>
              <Select
                placeholder={t('hrm:employee.form.selectGender', 'Chọn giới tính')}
                options={[
                  { value: 'male', label: t('hrm:employee.gender.male', 'Nam') },
                  { value: 'female', label: t('hrm:employee.gender.female', 'Nữ') },
                  { value: 'other', label: t('hrm:employee.gender.other', 'Khác') },
                ]}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="maritalStatus"
              label={t('hrm:employee.form.maritalStatus', 'Tình trạng hôn nhân')}
            >
              <Select
                placeholder={t('hrm:employee.form.selectMaritalStatus', 'Chọn tình trạng')}
                options={Object.values(MaritalStatus).map(status => ({
                  value: status,
                  label: t(`hrm:employee.maritalStatus.${status}`, status),
                }))}
                fullWidth
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        <CollapsibleCard
          title={
            <h3 className="text-lg font-medium p-3">
              {t('hrm:employee.form.contactInfo', 'Thông tin liên hệ')}
            </h3>
          }
          defaultOpen={true}
        >
          <div className="space-y-4">
            <FormItem name="address" label={t('hrm:employee.form.address', 'Địa chỉ')}>
              <Input leftIcon={<Icon name="map-pin" size="sm" />} fullWidth />
            </FormItem>

            <FormItem name="city" label={t('hrm:employee.form.city', 'Thành phố')}>
              <Input leftIcon={<Icon name="home" size="sm" />} fullWidth />
            </FormItem>

            <FormItem name="state" label={t('hrm:employee.form.state', 'Tỉnh/Bang')}>
              <Input leftIcon={<Icon name="map" size="sm" />} fullWidth />
            </FormItem>

            <FormItem name="country" label={t('hrm:employee.form.country', 'Quốc gia')}>
              <Input leftIcon={<Icon name="globe" size="sm" />} fullWidth />
            </FormItem>

            <FormItem name="postalCode" label={t('hrm:employee.form.postalCode', 'Mã bưu điện')}>
              <Input leftIcon={<Icon name="mail" size="sm" />} fullWidth />
            </FormItem>
          </div>
        </CollapsibleCard>

        <CollapsibleCard
          title={
            <h3 className="text-lg font-medium p-3">
              {t('hrm:employee.form.emergencyContact', 'Liên hệ khẩn cấp')}
            </h3>
          }
          defaultOpen={true}
        >
          <div className="space-y-4">
            <FormItem
              name="emergencyContactName"
              label={t('hrm:employee.form.emergencyContactName', 'Tên liên hệ')}
            >
              <Input leftIcon={<Icon name="user" size="sm" />} fullWidth />
            </FormItem>

            <FormItem
              name="emergencyContactPhone"
              label={t('hrm:employee.form.emergencyContactPhone', 'Số điện thoại')}
            >
              <Input leftIcon={<Icon name="phone" size="sm" />} fullWidth />
            </FormItem>

            <FormItem
              name="emergencyContactRelationship"
              label={t('hrm:employee.form.emergencyContactRelationship', 'Mối quan hệ')}
            >
              <Input leftIcon={<Icon name="users" size="sm" />} fullWidth />
            </FormItem>
          </div>
        </CollapsibleCard>

        <CollapsibleCard
          title={
            <h3 className="text-lg font-medium p-3">{t('hrm:employee.form.notes', 'Ghi chú')}</h3>
          }
          defaultOpen={true}
        >
          <FormItem name="notes" label={t('hrm:employee.form.notes', 'Ghi chú')}>
            <Textarea rows={4} fullWidth />
          </FormItem>
        </CollapsibleCard>

        <div className="flex justify-end space-x-4 mt-8">
          <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting}>
            {t('common:cancel', 'Hủy')}
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? t('common:saving', 'Đang lưu...') : t('common:save', 'Lưu')}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default EmployeeForm;
